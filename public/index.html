<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Message Recorder</title>
    <script src="https://res.cdn.office.net/teams-js/2.0.0/js/MicrosoftTeams.min.js"></script>
    <script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
    <script src="config.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            z-index: 1;
        }

        .app-container {
            position: relative;
            z-index: 2;
            width: 334px;
            height: 364px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 16px;
            box-shadow: 0 16px 32px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideIn 0.6s cubic-bezier(0.16, 1, 0.3, 1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px) scale(0.95); }
            to { opacity: 1; transform: translateY(0) scale(1); }
        }

        .app-header {
            text-align: center;
            margin-bottom: 12px;
            flex-shrink: 0;
        }

        .app-title {
            font-size: 18px;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-bottom: 2px;
            letter-spacing: -0.5px;
        }

        .app-subtitle {
            color: #64748b;
            font-size: 12px;
            font-weight: 400;
        }

        .recording-area {
            text-align: center;
            margin: 8px 0;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 140px;
        }

        .record-button-container {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
        }

        .status-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding-top: 10px;
        }

        .record-button {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
            box-shadow: 0 12px 24px rgba(255, 107, 107, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .record-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .record-button:hover:not(:disabled) {
            transform: scale(1.05);
            box-shadow: 0 25px 50px rgba(255, 107, 107, 0.4), 0 0 0 8px rgba(255, 107, 107, 0.1);
        }

        .record-button.recording {
            background: linear-gradient(135deg, #ff4757 0%, #c44569 100%);
            animation: recordingPulse 2s infinite;
        }

        @keyframes recordingPulse {
            0%, 100% { transform: scale(1); box-shadow: 0 20px 40px rgba(255, 71, 87, 0.4); }
            50% { transform: scale(1.08); box-shadow: 0 25px 50px rgba(255, 71, 87, 0.5), 0 0 0 15px rgba(255, 71, 87, 0.2); }
        }

        .record-icon {
            font-size: 28px;
            transition: all 0.3s ease;
        }

        .record-button.recording .record-icon {
            animation: iconBounce 1s infinite;
        }

        @keyframes iconBounce {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .status-message {
            font-size: 14px;
            font-weight: 500;
            color: #475569;
            min-height: 18px;
            transition: all 0.3s ease;
            white-space: normal;
            overflow: visible;
            text-overflow: initial;
            max-width: 100%;
        }

        .status-message.recording {
            display: none;
        }

        .recording-timer {
            font-size: 11px;
            color: #64748b;
            font-weight: 400;
            min-height: 14px;
        }

        .preview-section {
            margin: 8px 0;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
            flex-shrink: 0;
        }

        .preview-section.show {
            opacity: 1;
            transform: translateY(0);
        }

        .audio-preview {
            width: 100%;
            height: 35px;
            border-radius: 10px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            margin-bottom: 12px;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .action-btn {
            width: 42px;
            height: 42px;
            border-radius: 14px;
            border: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .action-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn-send {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            box-shadow: 0 8px 16px rgba(16, 185, 129, 0.3);
        }

        .btn-send:hover:not(:disabled) {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 12px 24px rgba(16, 185, 129, 0.4);
        }

        .btn-discard {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            box-shadow: 0 8px 16px rgba(239, 68, 68, 0.3);
        }

        .btn-discard:hover:not(:disabled) {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 12px 24px rgba(239, 68, 68, 0.4);
        }

        .status-alert {
            border-radius: 10px;
            padding: 8px 12px;
            font-weight: 500;
            font-size: 12px;
            margin: 6px 0;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
            color: white;
            text-align: center;
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        .status-alert.show {
            opacity: 1;
            transform: translateY(0);
        }

        .status-alert.alert-info {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        }

        .status-alert.alert-sending {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        }

        .status-alert.alert-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .status-alert.alert-error {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }

        .status-alert.alert-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        @media (max-width: 480px) {
            .app-container { width: 320px; height: 350px; padding: 12px; }
            .record-button { width: 70px; height: 70px; }
            .record-icon { font-size: 24px; }
            .action-btn { width: 38px; height: 38px; font-size: 14px; }
            .app-title { font-size: 16px; }
            .app-subtitle { font-size: 11px; }
        }
        .loader-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 10;
            border-radius: 20px;
            transition: opacity 0.3s ease;
        }

        .loader {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-bottom: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loader-container.hidden {
            opacity: 0;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="loader-container hidden" id="loader">
            <div class="loader"></div>
            <p>Loading...</p>
        </div>
        <div class="app-header">
            <h1 class="app-title">🎤 Voice Message</h1>
            <p class="app-subtitle">Record and send voice messages instantly</p>
        </div>
        <div class="recording-area" id="recordingArea">
            <div class="record-button-container">
                <button id="recordButton" class="record-button">
                    <i class="fas fa-microphone record-icon"></i>
                </button>
            </div>
            <div class="status-info">
                <div class="status-message" id="statusMessage">Tap to start recording</div>
                <div class="recording-timer" id="recordingTimer"></div>
            </div>
        </div>
        <div class="preview-section" id="previewSection">
            <audio id="preview" class="audio-preview" controls></audio>
            <div class="action-buttons">
                <button id="sendButton" class="action-btn btn-send"><i class="fas fa-paper-plane"></i></button>
                <button id="discardButton" class="action-btn btn-discard"><i class="fas fa-trash-alt"></i></button>
            </div>
        </div>
        <div id="statusAlert" class="status-alert"></div>
        <div class="retry-section" id="retrySection" style="display: none; text-align: center; margin-top: 10px;">
            <button id="retryButton" class="action-btn" style="background: #0078d4; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                <i class="fas fa-redo"></i> Retry Setup
            </button>
        </div>
    </div>

    <script>
        console.log('[index.html] Script loaded at', new Date().toISOString());

        // State management
        const state = {
            mediaRecorder: null,
            audioChunks: [],
            recordingTimer: null,
            recordingDuration: 0,
            stream: null,
            socket: null,
            authCache: { delegatedToken: null, tokenExpiry: null, userContext: null, isAuthenticated: false },
            preFetchedData: { accessToken: null, chatMembers: null, folderReady: false, userContext: null },
            isAppReady: false
        };

        // Utility functions
        const logAndShow = (message, type = 'info', showSpinner = false, disableRecording = null) => {
            console.log(`[index.html] ${message}`);
            const statusAlert = document.getElementById('statusAlert');
            const statusMessage = document.getElementById('statusMessage');
            statusMessage.textContent = message.replace(/<[^>]*>/g, ''); // Remove HTML tags for statusMessage
            const icons = {
                sending: showSpinner ? '<i class="fas fa-spinner fa-spin"></i> ' : '<i class="fas fa-paper-plane"></i> ',
                error: '<i class="fas fa-exclamation-triangle"></i> ',
                success: '<i class="fas fa-check-circle"></i> ',
                warning: showSpinner ? '<i class="fas fa-spinner fa-spin"></i> ' : '<i class="fas fa-info-circle"></i> ',
                info: ''
            };
            statusAlert.innerHTML = `${icons[type] || ''}${message}${type === 'sending' || type === 'warning' ? '<br><small>Please keep this window open</small>' : ''}`;
            statusAlert.classList.remove('alert-info', 'alert-sending', 'alert-success', 'alert-error', 'alert-warning', 'show');
            statusAlert.classList.add(`alert-${type}`, 'show');
            setTimeout(() => statusAlert.classList.remove('show'), 5000);

            // Control record button state more precisely
            if (disableRecording === true || (disableRecording === null && (type === 'error' || type === 'warning'))) {
                document.getElementById('recordButton').disabled = true;
                document.getElementById('recordButton').style.cursor = 'not-allowed';
            } else if (disableRecording === false) {
                document.getElementById('recordButton').disabled = false;
                document.getElementById('recordButton').style.cursor = 'pointer';
            }
        };

        const detectOS = () => {
            const ua = navigator.userAgent.toLowerCase();
            if (ua.includes('linux') || ua.includes('ubuntu')) return 'linux';
            if (ua.includes('win')) return 'windows';
            if (ua.includes('mac')) return 'mac';
            return 'unknown';
        };

        const isTeamsDesktopClient = () => navigator.userAgent.toLowerCase().includes('teams/') || navigator.userAgent.toLowerCase().includes('msteams') || navigator.userAgent.toLowerCase().includes('teams-for-linux') || navigator.userAgent.toLowerCase().includes('electron');

        const showLinuxMessage = () => {
            logAndShow('❌ Please use Teams in your web browser on Ubuntu/Linux', 'error');
            document.getElementById('statusMessage').innerHTML = `
                <div style="background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%); color: #92400e; border: 1px solid #fcd34d; border-radius: 8px; padding: 8px 10px; font-size: 11px; font-weight: 500; line-height: 1.3; box-shadow: 0 2px 4px rgba(251, 191, 36, 0.2); animation: slideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);">
                    <div style="font-weight: 600; margin-bottom: 4px;">⚠️ Ubuntu Teams-for-Linux Users</div>
                    "Popups blocked in desktop client</div>
                    <a href="https://teams.microsoft.com" target="_blank" style="color:#1d4ed8; text-decoration: none; font-weight: 600; display: inline-flex; align-items: center; gap: 4px;">🌐 Open Teams in Browser</a>
                </div>
            `;
        };

        // Authentication and token management
        const getValidToken = async () => {
            if (state.authCache.isAuthenticated && state.authCache.delegatedToken) {
                try {
                    const response = await fetch('https://graph.microsoft.com/v1.0/me', {
                        headers: { 'Authorization': `Bearer ${state.authCache.delegatedToken}` }
                    });
                    if (response.ok) {
                        sessionStorage.setItem('delegatedToken', state.authCache.delegatedToken);
                        return state.authCache.delegatedToken;
                    }
                } catch (err) {}
            }
            const sessionToken = sessionStorage.getItem('delegatedToken');
            if (sessionToken) {
                try {
                    const response = await fetch('https://graph.microsoft.com/v1.0/me', {
                        headers: { 'Authorization': `Bearer ${sessionToken}` }
                    });
                    if (response.ok) {
                        state.authCache.delegatedToken = sessionToken;
                        state.authCache.isAuthenticated = true;
                        return sessionToken;
                    }
                } catch (err) {}
            }
            return new Promise((resolve, reject) => {
                microsoftTeams.authentication.authenticate({
                    url: window.APP_CONFIG.getAuthUrl(),
                    width: 600,
                    height: 400,
                    successCallback: (token) => {
                        state.authCache.delegatedToken = token;
                        state.authCache.isAuthenticated = true;
                        sessionStorage.setItem('delegatedToken', token);
                        resolve(token);
                    },
                    failureCallback: (error) => {
                        logAndShow(`❌ Authentication failed: ${error}`, 'error');
                        reject(error);
                    }
                });
            });
        };

        // Socket initialization
        const initializeSocket = () => {
            if (state.socket?.connected) return Promise.resolve(state.socket);
            return new Promise((resolve, reject) => {
                state.socket = io(window.APP_CONFIG.SOCKET_URL, {
                    path: '/socket.io',
                    transports: ['polling'],
                    reconnection: true,
                    reconnectionAttempts: 5,
                    reconnectionDelay: 1000,
                    timeout: 60000,
                    query: { client: 'teams', timestamp: Date.now() }
                });

                state.socket.on('connect', () => {
                    console.log('[index.html] Socket.IO connected');
                    microsoftTeams.getContext((context) => {
                        if (!context.userObjectId && !context.userId) {
                            logAndShow('❌ Failed to retrieve user information.', 'error');
                            reject(new Error('No user ID in context'));
                            return;
                        }
                        state.socket.emit('initialize-context', {
                            userObjectId: context.userObjectId || context.userId,
                            userDisplayName: context.userPrincipalName || 'Unknown User',
                            chatId: context.chatId || context.channelId || null
                        });
                        resolve(state.socket);
                    });
                });

                state.socket.on('connect_error', (error) => {
                    console.error('[index.html] Socket.IO connection error:', error);
                    logAndShow(`❌ Cannot connect to server: ${error.message}.`, 'error');
                    reject(error);
                });

                state.socket.on('disconnect', () => {
                    console.log('[index.html] Socket.IO disconnected');
                    state.socket = null;
                });

                state.socket.on('upload-success', (data) => {
                    logAndShow('✅ Voice message sent successfully!', 'success');
                    const result = {
                        messageId: data.messageId,
                        downloadUrl: data.downloadUrl,
                        fileName: data.fileName,
                        chatId: data.chatId,
                        userId: data.userId,
                        userName: data.userName,
                        status: 'complete'
                    };
                    cleanupAndClose(result);
                });

                state.socket.on('upload-error', (error) => {
                    console.error('[index.html] Upload error:', error);
                    const errorMessage = error.details || error.error;
                    logAndShow(`❌ Failed to send message: ${errorMessage}.`, 'error');
                    document.getElementById('sendButton').disabled = false;
                    document.getElementById('discardButton').disabled = false;
                    if (error.shouldClose) cleanupAndClose({ success: false, error: errorMessage, details: error.details, timestamp: new Date().toISOString() }, 3000);
                });

                state.socket.on('closeApp', (data) => {
                    logAndShow(`❌ ${data.reason}`, 'error');
                    cleanupAndClose({ success: false, error: data.reason || 'App closed by server', details: data.error, timestamp: new Date().toISOString() }, 2000);
                });

                setTimeout(() => {
                    if (!state.socket?.connected) {
                        logAndShow('❌ Connection timeout. Please try again.', 'error');
                        reject(new Error('Connection timeout'));
                    }
                }, 60000);
            });
        };

        const cleanupAndClose = (result, delay = 2000) => {
            if (state.stream) {
                state.stream.getTracks().forEach(track => track.stop());
                state.stream = null;
            }
            state.audioChunks = [];
            if (state.socket?.connected) {
                state.socket.disconnect();
                state.socket = null;
            }
            setTimeout(() => {
                try {
                    if (typeof microsoftTeams !== 'undefined') {
                        microsoftTeams.tasks.submitTask(result);
                    } else {
                        window.close();
                    }
                } catch (error) {
                    console.log('[index.html] Could not close via Teams API, trying window.close()');
                    window.close();
                }
            }, delay);
        };

        // Microphone permission
        const requestMicrophonePermission = async () => {
            let permissionResolved = false;
            const permissionTimeout = setTimeout(() => {
                if (!permissionResolved) logAndShow('❌ Please enable microphone access: Teams Settings → Apps → Citrus Voice Message → Permissions → Allow microphone', 'error');
            }, 1000);
            try {
                state.stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                permissionResolved = true;
                clearTimeout(permissionTimeout);
                return true;
            } catch (error) {
                permissionResolved = true;
                clearTimeout(permissionTimeout);
                console.error('[index.html] Microphone permission error:', error);
                if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
                    logAndShow('❌ Please enable microphone access: Teams Settings → Apps → Citrus Voice Message → Permissions → Allow microphone', 'error');
                } else if (error.name === 'NotFoundError') {
                    logAndShow('❌ No microphone found. Please connect a microphone.', 'error');
                } else {
                    logAndShow('❌ Microphone access failed. Please check device settings.', 'error');
                }
                return false;
            }
        };

        // Pre-fetch critical data
        const preFetchCriticalData = async () => {
            try {
                const [context, delegatedToken] = await Promise.all([
                    new Promise(resolve => microsoftTeams.getContext(resolve)),
                    getValidToken()
                ]);

                state.authCache.userContext = context;
                if (!context.userObjectId && !context.userId) throw new Error('No user ID in context');
                state.preFetchedData.userContext = context;
                const authHeaders = { 'Content-Type': 'application/json', 'Authorization': `Bearer ${delegatedToken}` };

                // Execute API calls in parallel
                const [accessTokenResult, chatMembersResult, folderResult] = await Promise.all([
                    fetch('/api/get-access-token', {
                        method: 'POST',
                        headers: authHeaders,
                        body: JSON.stringify({ chatId: context.chatId || context.channelId, userObjectId: context.userObjectId || context.userId })
                    }).then(async res => {
                        if (!res.ok) {
                            const errorData = await res.json();
                            return { error: errorData.message || 'Failed to fetch access token', statusCode: res.status };
                        }
                        return res.json();
                    }).catch(err => ({ error: err.message || 'API call failed', statusCode: 500 })),

                    fetch('/api/get-chat-members', {
                        method: 'POST',
                        headers: authHeaders,
                        body: JSON.stringify({ chatId: context.chatId || context.channelId })
                    }).then(async res => {
                        if (!res.ok) {
                            const errorData = await res.json();
                            return { error: errorData.message || 'Failed to fetch chat members', statusCode: res.status, code: errorData.code };
                        }
                        return res.json();
                    }).catch(err => ({ error: err.message || 'API call failed', statusCode: 500 })),

                    fetch('/api/ensure-teams-chat-files-folder', {
                        method: 'POST',
                        headers: authHeaders,
                        body: JSON.stringify({ userObjectId: context.userObjectId || context.userId })
                    }).then(async res => {
                        if (!res.ok) {
                            const errorData = await res.json();
                            return { error: errorData.message || 'Failed to ensure Teams Chat Files folder', statusCode: res.status };
                        }
                        return res.json();
                    }).catch(err => ({ error: err.message || 'API call failed', statusCode: 500 }))
                ]);

                // Map error messages based on server.js logic
                const errorMessages = {
                    accessToken: {
                        default: `❌ Failed to fetch access token: `
                    },
                    chatMembers: {
                        default: `❌ Failed to fetch chat members: `
                    },
                    folder: {
                        default: `❌ Failed to ensure Teams Chat Files folder: `
                    }
                };

                // Process errors
                const errors = [];
                if (accessTokenResult.error) {
                    const message = errorMessages.accessToken[accessTokenResult.statusCode] || errorMessages.accessToken.default;
                    errors.push(message + accessTokenResult.error);
                }
                if (chatMembersResult.error) {
                    const message = errorMessages.chatMembers[chatMembersResult.code] || errorMessages.chatMembers[chatMembersResult.statusCode] || errorMessages.chatMembers.default;
                    errors.push(message + chatMembersResult.error);
                }
                if (folderResult.error) {
                    const message = errorMessages.folder[folderResult.statusCode] || errorMessages.folder.default;
                    errors.push(message + folderResult.error);
                }

                if (errors.length) {
                    errors.forEach(err => logAndShow(err, 'error'));
                    return false;
                }

                // Store successful results
                state.preFetchedData.accessToken = accessTokenResult.accessToken;
                state.preFetchedData.chatMembers = chatMembersResult.members;
                state.preFetchedData.folderReady = true;

                state.isAppReady = true;
                console.log('[index.html] ✅ All pre-fetch operations completed');
                return true;
            } catch (error) {
                console.error('[index.html] Pre-fetch error:', error);
                logAndShow(`❌ App Setup Error: ${error.message}`, 'error');
                return false;
            }
        };

        // Background version of preFetchCriticalData with enhanced error handling
        const preFetchCriticalDataInBackground = async () => {
            try {
                console.log('[index.html] Starting background data fetch...');

                const [context, delegatedToken] = await Promise.all([
                    new Promise(resolve => microsoftTeams.getContext(resolve)),
                    getValidToken()
                ]);

                state.authCache.userContext = context;
                if (!context.userObjectId && !context.userId) throw new Error('No user ID in context');
                state.preFetchedData.userContext = context;
                const authHeaders = { 'Content-Type': 'application/json', 'Authorization': `Bearer ${delegatedToken}` };

                // Execute API calls in parallel with individual error handling
                const [accessTokenResult, chatMembersResult, folderResult] = await Promise.all([
                    fetch('/api/get-access-token', {
                        method: 'POST',
                        headers: authHeaders,
                        body: JSON.stringify({ chatId: context.chatId || context.channelId, userObjectId: context.userObjectId || context.userId })
                    }).then(async res => {
                        if (!res.ok) {
                            const errorData = await res.json();
                            return { error: errorData.message || 'Failed to fetch access token', statusCode: res.status };
                        }
                        return res.json();
                    }).catch(err => ({ error: err.message || 'Network error during access token fetch', statusCode: 500 })),

                    fetch('/api/get-chat-members', {
                        method: 'POST',
                        headers: authHeaders,
                        body: JSON.stringify({ chatId: context.chatId || context.channelId })
                    }).then(async res => {
                        if (!res.ok) {
                            const errorData = await res.json();
                            return { error: errorData.message || 'Failed to fetch chat members', statusCode: res.status, code: errorData.code };
                        }
                        return res.json();
                    }).catch(err => ({ error: err.message || 'Network error during chat members fetch', statusCode: 500 })),

                    fetch('/api/ensure-teams-chat-files-folder', {
                        method: 'POST',
                        headers: authHeaders,
                        body: JSON.stringify({ userObjectId: context.userObjectId || context.userId })
                    }).then(async res => {
                        if (!res.ok) {
                            const errorData = await res.json();
                            return { error: errorData.message || 'Failed to ensure Teams Chat Files folder', statusCode: res.status };
                        }
                        return res.json();
                    }).catch(err => ({ error: err.message || 'Network error during folder setup', statusCode: 500 }))
                ]);

                // Process results and handle errors gracefully
                const errors = [];
                let hasAccessToken = false;
                let hasChatMembers = false;
                let hasFolderReady = false;

                if (accessTokenResult.error) {
                    errors.push(`❌ Failed to fetch access token: ${accessTokenResult.error}`);
                } else {
                    state.preFetchedData.accessToken = accessTokenResult.accessToken;
                    hasAccessToken = true;
                }

                if (chatMembersResult.error) {
                    // Special handling for 404 errors (users who haven't chatted before)
                    if (chatMembersResult.statusCode === 404) {
                        errors.push(`❌ Cannot send voice messages: You haven't chatted with this person before. Please send a text message first.`);
                    } else {
                        errors.push(`❌ Failed to fetch chat members: ${chatMembersResult.error}`);
                    }
                } else {
                    state.preFetchedData.chatMembers = chatMembersResult.members;
                    hasChatMembers = true;
                }

                if (folderResult.error) {
                    errors.push(`❌ Failed to setup file storage: ${folderResult.error}`);
                } else {
                    state.preFetchedData.folderReady = true;
                    hasFolderReady = true;
                }

                // Display errors using logAndShow
                if (errors.length > 0) {
                    errors.forEach(error => {
                        logAndShow(error, 'error', false, false); // Don't disable recording for individual errors
                    });

                    // If critical errors occurred, disable recording and show retry button
                    if (!hasAccessToken || !hasChatMembers || !hasFolderReady) {
                        logAndShow('❌ Recording disabled due to setup errors. Click retry to try again.', 'error', false, true);
                        state.isAppReady = false;
                        showRetryButton();
                        return false;
                    }
                }

                // If we get here, all critical data was fetched successfully
                state.isAppReady = true;
                console.log('[index.html] ✅ Background data fetch completed successfully');
                logAndShow('✅ App ready for voice messaging', 'success', false, false);

                // Re-enable record button if it was disabled and hide retry button
                const recordButton = document.getElementById('recordButton');
                recordButton.disabled = false;
                recordButton.style.cursor = 'pointer';
                hideRetryButton();

                return true;

            } catch (error) {
                console.error('[index.html] Background pre-fetch error:', error);
                logAndShow(`❌ App Setup Error: ${error.message}. Click retry to try again.`, 'error', false, true);
                state.isAppReady = false;
                showRetryButton();
                return false;
            }
        };

        // Helper functions for retry functionality
        const showRetryButton = () => {
            try {
                document.getElementById('retrySection').style.display = 'block';
            } catch (error) {
                console.warn('[index.html] Error showing retry button:', error);
            }
        };

        const hideRetryButton = () => {
            try {
                document.getElementById('retrySection').style.display = 'none';
            } catch (error) {
                console.warn('[index.html] Error hiding retry button:', error);
            }
        };

        // Retry button handler
        document.getElementById('retryButton').addEventListener('click', async () => {
            try {
                const retryButton = document.getElementById('retryButton');
                retryButton.disabled = true;
                retryButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Retrying...';

                logAndShow('Retrying app setup...', 'info');

                // Reset state
                state.preFetchedData = { accessToken: null, chatMembers: null, folderReady: false, userContext: null };
                state.isAppReady = false;

                // Retry the background fetch
                const success = await preFetchCriticalDataInBackground();

                // Re-enable retry button
                retryButton.disabled = false;
                retryButton.innerHTML = '<i class="fas fa-redo"></i> Retry Setup';

                if (!success) {
                    logAndShow('❌ Retry failed. Please check your connection and try again.', 'error');
                }
            } catch (error) {
                console.error('[index.html] Error during retry:', error);
                logAndShow(`❌ Retry failed: ${error.message}`, 'error');

                // Re-enable retry button
                const retryButton = document.getElementById('retryButton');
                retryButton.disabled = false;
                retryButton.innerHTML = '<i class="fas fa-redo"></i> Retry Setup';
            }
        });

        // Initialize app
        if (typeof microsoftTeams !== 'undefined') {
            microsoftTeams.initialize(async () => {
                microsoftTeams.appInitialization.notifyAppLoaded();
                microsoftTeams.appInitialization.notifySuccess();

                const os = detectOS();
                if (os === 'linux' && isTeamsDesktopClient()) {
                    showLinuxMessage();
                    return;
                }

                // Request microphone permission immediately
                const microphonePermissionGranted = await requestMicrophonePermission();
                if (!microphonePermissionGranted) {
                    state.isAppReady = false;
                    return;
                }

                // Start preFetchCriticalData in background - don't wait for it
                preFetchCriticalDataInBackground();

                // App is ready for basic interaction (recording will be disabled until background fetch completes)
                state.isAppReady = true;
                logAndShow('App ready - preparing data in background...', 'info');
            });
        } else {
            logAndShow('❌ Teams SDK failed to load. Please refresh.', 'error');
        }

        // Record button handler with enhanced error handling
        document.getElementById('recordButton').addEventListener('click', async () => {
            try {
                if (!state.isAppReady) {
                    logAndShow('❌ App not ready. Please resolve errors and try again.', 'error');
                    return;
                }

                if (state.mediaRecorder?.state === 'recording') {
                    // Stop recording
                    try {
                        state.mediaRecorder.stop();
                        clearInterval(state.recordingTimer);
                        document.getElementById('recordButton').classList.remove('recording');
                        document.getElementById('recordButton').innerHTML = '<i class="fas fa-microphone record-icon"></i>';
                        document.getElementById('recordingArea').classList.remove('recording');
                        document.getElementById('statusMessage').classList.remove('recording');
                        document.getElementById('statusMessage').textContent = 'Recording complete';
                        document.getElementById('recordingTimer').textContent = '';
                        document.getElementById('previewSection').classList.add('show');
                    } catch (stopError) {
                        console.error('[index.html] Error stopping recording:', stopError);
                        logAndShow(`❌ Error stopping recording: ${stopError.message}`, 'error');
                        // Reset UI state even if stop failed
                        resetRecordingUI();
                    }
                } else {
                    // Start recording
                    if (!state.stream) {
                        const permissionGranted = await requestMicrophonePermission();
                        if (!permissionGranted) {
                            logAndShow('❌ Microphone permission required to record', 'error');
                            return;
                        }
                    }

                    // Create MediaRecorder with error handling
                    try {
                        state.mediaRecorder = new MediaRecorder(state.stream, {
                            mimeType: 'audio/webm;codecs=opus',
                            audioBitsPerSecond: 8000
                        });
                    } catch (mediaRecorderError) {
                        console.error('[index.html] Error creating MediaRecorder:', mediaRecorderError);
                        logAndShow(`❌ Recording not supported: ${mediaRecorderError.message}`, 'error');
                        return;
                    }

                    state.audioChunks = [];
                    state.recordingDuration = 0;

                    // Set up MediaRecorder event handlers with error handling
                    state.mediaRecorder.ondataavailable = (event) => {
                        try {
                            if (event.data.size > 0) {
                                state.audioChunks.push(event.data);
                            }
                        } catch (dataError) {
                            console.error('[index.html] Error handling audio data:', dataError);
                            logAndShow(`❌ Recording data error: ${dataError.message}`, 'error');
                        }
                    };

                    state.mediaRecorder.onstop = () => {
                        try {
                            const audioBlob = new Blob(state.audioChunks, { type: 'audio/webm' });
                            document.getElementById('preview').src = URL.createObjectURL(audioBlob);
                        } catch (blobError) {
                            console.error('[index.html] Error creating audio blob:', blobError);
                            logAndShow(`❌ Error processing recording: ${blobError.message}`, 'error');
                        }
                    };

                    state.mediaRecorder.onerror = (errorEvent) => {
                        console.error('[index.html] MediaRecorder error:', errorEvent);
                        logAndShow(`❌ Recording error: ${errorEvent.error?.message || 'Unknown recording error'}`, 'error');
                        resetRecordingUI();
                    };

                    // Start recording with error handling
                    try {
                        state.mediaRecorder.start(1000);
                        document.getElementById('recordButton').classList.add('recording');
                        document.getElementById('recordButton').innerHTML = '<i class="fas fa-stop record-icon"></i>';
                        document.getElementById('recordingArea').classList.add('recording');
                        document.getElementById('statusMessage').classList.add('recording');
                        document.getElementById('previewSection').classList.remove('show');

                        state.recordingTimer = setInterval(() => {
                            try {
                                state.recordingDuration++;
                                const minutes = Math.floor(state.recordingDuration / 60);
                                const seconds = state.recordingDuration % 60;
                                document.getElementById('recordingTimer').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                                if (state.recordingDuration >= 300) {
                                    state.mediaRecorder.stop();
                                    clearInterval(state.recordingTimer);
                                    logAndShow('Recording stopped (max duration reached)', 'info');
                                }
                            } catch (timerError) {
                                console.error('[index.html] Error in recording timer:', timerError);
                                clearInterval(state.recordingTimer);
                            }
                        }, 1000);
                    } catch (startError) {
                        console.error('[index.html] Error starting recording:', startError);
                        logAndShow(`❌ Failed to start recording: ${startError.message}`, 'error');
                        resetRecordingUI();
                    }
                }
            } catch (error) {
                console.error('[index.html] Unexpected error in record button handler:', error);
                logAndShow(`❌ Recording error: ${error.message}`, 'error');
                resetRecordingUI();
            }
        });

        // Helper function to reset recording UI state
        const resetRecordingUI = () => {
            try {
                if (state.recordingTimer) {
                    clearInterval(state.recordingTimer);
                    state.recordingTimer = null;
                }
                document.getElementById('recordButton').classList.remove('recording');
                document.getElementById('recordButton').innerHTML = '<i class="fas fa-microphone record-icon"></i>';
                document.getElementById('recordingArea').classList.remove('recording');
                document.getElementById('statusMessage').classList.remove('recording');
                document.getElementById('statusMessage').textContent = 'Tap to start recording';
                document.getElementById('recordingTimer').textContent = '';
                document.getElementById('previewSection').classList.remove('show');

                // Clean up media recorder
                if (state.mediaRecorder && state.mediaRecorder.state !== 'inactive') {
                    try {
                        state.mediaRecorder.stop();
                    } catch (e) {
                        console.warn('[index.html] Error stopping media recorder during cleanup:', e);
                    }
                }
                state.mediaRecorder = null;
                state.audioChunks = [];
                state.recordingDuration = 0;
            } catch (resetError) {
                console.error('[index.html] Error resetting recording UI:', resetError);
            }
        };

        // Send button handler with enhanced error handling
        document.getElementById('sendButton').addEventListener('click', async () => {
            if (state.audioChunks.length === 0) {
                logAndShow('Please record a message first', 'error');
                return;
            }

            // Disable buttons immediately to prevent double-clicks
            const sendButton = document.getElementById('sendButton');
            const discardButton = document.getElementById('discardButton');
            sendButton.disabled = true;
            discardButton.disabled = true;

            try {
                // Check if app is ready and data is available
                if (!state.isAppReady) {
                    throw new Error('App not ready. Please wait for setup to complete.');
                }

                logAndShow('Connecting to server...', 'sending', true);

                // Initialize socket with timeout
                const socketPromise = initializeSocket();
                const timeoutPromise = new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Connection timeout')), 10000)
                );

                await Promise.race([socketPromise, timeoutPromise]);

                // Create audio blob with error handling
                let audioBlob;
                try {
                    audioBlob = new Blob(state.audioChunks, { type: 'audio/webm' });
                    if (audioBlob.size === 0) {
                        throw new Error('Recording is empty');
                    }
                } catch (blobError) {
                    throw new Error(`Invalid recording data: ${blobError.message}`);
                }

                // Get token with error handling
                let delegatedToken;
                try {
                    delegatedToken = await getValidToken();
                    if (!delegatedToken) {
                        throw new Error('Authentication failed - no token available');
                    }
                } catch (tokenError) {
                    throw new Error(`Authentication error: ${tokenError.message}`);
                }

                logAndShow('Preparing voice message...', 'sending', true);

                // Process audio with error handling
                const reader = new FileReader();

                reader.onerror = () => {
                    throw new Error('Failed to process audio file');
                };

                reader.onloadend = async () => {
                    try {
                        const base64Audio = reader.result.split(',')[1];
                        if (!base64Audio) {
                            throw new Error('Failed to encode audio data');
                        }

                        const context = state.authCache.userContext || state.preFetchedData.userContext;
                        if (!context) {
                            throw new Error('No user context available - please refresh the app');
                        }

                        // Validate required data
                        if (!state.preFetchedData.accessToken) {
                            throw new Error('Access token not available - please refresh the app');
                        }

                        if (!state.preFetchedData.chatMembers) {
                            throw new Error('Chat members not available - you may need to send a text message first');
                        }

                        if (!state.preFetchedData.folderReady) {
                            throw new Error('File storage not ready - please refresh the app');
                        }

                        logAndShow('Sending voice message...', 'sending', true);

                        // Emit with error handling
                        if (!state.socket || !state.socket.connected) {
                            throw new Error('Connection lost - please try again');
                        }

                        state.socket.emit('upload-voice-message', {
                            audioBlob: base64Audio,
                            senderId: context.userObjectId || context.userId,
                            chatType: context.chatId ? 'oneOnOne' : 'channel',
                            teamId: context.teamId,
                            channelId: context.chatId || context.channelId,
                            delegatedToken,
                            preFetchedData: state.preFetchedData
                        });

                    } catch (processingError) {
                        console.error('[index.html] Error processing message:', processingError);
                        logAndShow(`❌ Failed to send message: ${processingError.message}`, 'error');
                        enableSendButtons();
                        if (state.socket?.connected) {
                            try {
                                state.socket.disconnect();
                            } catch (disconnectError) {
                                console.warn('[index.html] Error disconnecting socket:', disconnectError);
                            }
                        }
                    }
                };

                reader.readAsDataURL(audioBlob);

            } catch (error) {
                console.error('[index.html] Error sending message:', error);
                logAndShow(`❌ Failed to send message: ${error.message}`, 'error');
                enableSendButtons();

                // Clean up socket connection
                if (state.socket?.connected) {
                    try {
                        state.socket.disconnect();
                    } catch (disconnectError) {
                        console.warn('[index.html] Error disconnecting socket:', disconnectError);
                    }
                }
            }
        });

        // Helper function to re-enable send buttons
        const enableSendButtons = () => {
            try {
                document.getElementById('sendButton').disabled = false;
                document.getElementById('discardButton').disabled = false;
            } catch (error) {
                console.warn('[index.html] Error enabling buttons:', error);
            }
        };

        // Discard button handler
        document.getElementById('discardButton').addEventListener('click', () => {
            state.audioChunks = [];
            document.getElementById('previewSection').classList.remove('show');
            document.getElementById('statusMessage').classList.remove('recording');
            document.getElementById('statusMessage').textContent = 'Tap to start recording';
            document.getElementById('recordingTimer').textContent = '';
            logAndShow('Recording discarded', 'info');
            if (state.stream) {
                state.stream.getTracks().forEach(track => track.stop());
                state.stream = null;
            }
        });

        console.log('[index.html] Script initialization complete');
    </script>
</body>
</html>